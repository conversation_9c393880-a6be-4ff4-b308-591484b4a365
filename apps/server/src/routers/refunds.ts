import { RefundRequestModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const RefundRequestListSchema = createListOutputSchema(
	RefundRequestModelSchema,
);
export const refunds = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.refundRequest.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(RefundRequestListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.refundRequest, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};

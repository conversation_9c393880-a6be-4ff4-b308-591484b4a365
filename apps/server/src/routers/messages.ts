import { SupportMessageModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const SupportMessageListSchema = createListOutputSchema(
	SupportMessageModelSchema,
);
export const messages = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.supportMessage.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(SupportMessageListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.supportMessage, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.supportMessage.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};

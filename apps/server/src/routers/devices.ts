import { DeviceModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const DeviceListSchema = createListOutputSchema(DeviceModelSchema);
export const devices = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.device.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(DeviceListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.device, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.device.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};

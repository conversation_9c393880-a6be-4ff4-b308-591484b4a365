import { AccountModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const AccountListSchema = createListOutputSchema(AccountModelSchema);
export const accounts = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.account.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(AccountListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.account, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.account.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};

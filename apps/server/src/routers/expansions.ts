import {
	DeviceExpansionModelSchema,
	RefundRequestModelSchema,
} from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const DeviceExpansionListSchema = createListOutputSchema(
	DeviceExpansionModelSchema,
);
export const expansions = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.deviceExpansion.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(DeviceExpansionListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.deviceExpansion, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};

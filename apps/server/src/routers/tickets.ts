import { SupportTicketModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const SupportTicketListSchema = createListOutputSchema(
	SupportTicketModelSchema,
);
export const tickets = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.supportTicket.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(SupportTicketListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.supportTicket, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.supportTicket.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};

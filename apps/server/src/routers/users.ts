import { UserModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const UserListSchema = createListOutputSchema(UserModelSchema);
export const users = {
	getMe: protectedProcedure.handler(({ context }) => {
		return context.session?.user;
	}),
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.user.findMany({
			where: {
				NOT: { id: { equals: context.session?.user.id } },
			},
		});
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(UserListSchema)
		.handler(async ({ input, context }) => {
			const { search, filters } = input;

			let where: any = { NOT: { id: { equals: context.session?.user.id } } };

			if (search) {
				where = {
					...where,
					OR: [{ name: { contains: search } }, { email: { contains: search } }],
					...filters,
				};
			}

			return await buildListResponse(prisma.user, input, where);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.user.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};

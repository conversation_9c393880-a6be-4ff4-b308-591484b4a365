
import { useMutation, useQueryClient } from "@tanstack/react-query";

type OptimisticUpdateConfig<TData, TVariables> = {
	queryKey: unknown[];
	updateFn: (
		oldData: TData | undefined,
		variables: TVariables,
	) => TData | undefined;
	mutationFn: (variables: TVariables) => Promise<any>;
	onError?: (error: Error, variables: TVariables, context: any) => void;
	onSuccess?: (data: any, variables: TVariables, context: any) => void;
};

export function useOptimisticMutation<TData, TVariables>({
	queryKey,
	updateFn,
	mutationFn,
	onError,
	onSuccess,
}: OptimisticUpdateConfig<TData, TVariables>) {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn,
		onMutate: async (variables) => {
			// Cancel outgoing refetches
			await queryClient.cancelQueries({ queryKey });

			// Snapshot previous value
			const previousData = queryClient.getQueryData<TData>(queryKey);

			// Optimistically update
			queryClient.setQueryData<TData>(queryKey, (oldData) =>
				updateFn(oldData, variables),
			);

			return { previousData };
		},
		onError: (error, variables, context) => {
			// Rollback on error
			if (context?.previousData !== undefined) {
				queryClient.setQueryData(queryKey, context.previousData);
			}
			onError?.(error, variables, context);
		},
		onSuccess: (data, variables, context) => {
			onSuccess?.(data, variables, context);
		},
		onSettled: () => {
			// Always refetch to ensure consistency
			queryClient.invalidateQueries({ queryKey });
		},
	});
}

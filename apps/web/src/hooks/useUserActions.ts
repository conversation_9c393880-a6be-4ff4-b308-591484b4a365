import { createOptimisticListActions } from "@/utils/createOptimisticActions";
import { orpc } from "@/utils/orpc";

type User = {
	id: string;
	name: string;
	email: string;
	role: string;
};

export const useUserActions = (
	params: { page?: number; limit?: number } = {},
) =>
	createOptimisticListActions<User>(
		[
			...orpc.users.paginate.queryOptions({
				input: { page: params.page ?? 1, limit: params.limit ?? 10 },
			}).queryKey,
		],
		{
			update: async (variables) => orpc.users.update.mutate(variables),
			delete: async (id) => orpc.users.delete.mutate({ id }),
			create: async (variables) => orpc.users.create.mutate(variables),
		},
	);

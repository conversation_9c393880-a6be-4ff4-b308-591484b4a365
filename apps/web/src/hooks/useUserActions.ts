import { createOptimisticListActions } from "@/utils/createOptimisticActions";
import { orpc } from "@/utils/orpc";

type User = {
	id: string;
	name: string;
	email: string;
	role: string;
};

export const useUserActions = createOptimisticListActions<User>(
	[
		...orpc.users.paginate.queryOptions({ input: { page: 1, limit: 10 } })
			.queryKey,
	],
	{
		update: async (_variables) => {
			throw new Error(
				"User update not implemented - users are managed through authentication system",
			);
		},
		delete: async (_id) => {
			throw new Error(
				"User deletion not implemented - users are managed through authentication system",
			);
		},
		create: async (_variables) => {
			throw new Error(
				"User creation not implemented - users are created through sign-up process",
			);
		},
	},
);

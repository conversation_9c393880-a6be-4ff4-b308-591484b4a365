import { orpc } from "@/utils/orpc";
import { createOptimisticListActions } from "@/utils/createOptimisticActions";

type User = {
  id: string;
  name: string;
  email: string;
  role: string;
};

export const useUserActions = createOptimisticListActions<User>(
  orpc.users.paginate.getQueryKey({ page: 1, limit: 10 }),
  {
    update: (variables) => orpc.users.update.mutate(variables),
    delete: (id) => orpc.users.delete.mutate({ id }),
    create: (variables) => orpc.users.create.mutate(variables),
  }
);
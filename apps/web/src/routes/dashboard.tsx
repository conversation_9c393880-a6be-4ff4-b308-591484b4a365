import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { useEffect } from "react";
import { authClient } from "@/lib/auth-client";
import { orpc } from "@/utils/orpc";

export const Route = createFileRoute("/dashboard")({
	component: RouteComponent,
});

function RouteComponent() {
	const { data: session, isPending } = authClient.useSession();

	const navigate = Route.useNavigate();

	const me = useQuery(orpc.users.getMe.queryOptions());
	const users = useQuery(
		orpc.users.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);

	const licenses = useQuery(
		orpc.licenses.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);

	const devices = useQuery(
		orpc.devices.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);

	const payments = useQuery(
		orpc.payments.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);

	useEffect(() => {
		if (!session && !isPending) {
			navigate({
				to: "/login",
			});
		}
	}, [session, isPending, navigate]);

	if (isPending) {
		return <div>Loading...</div>;
	}

	console.log(users.data);
	console.log(licenses.data);
	console.log(devices.data);
	console.log(payments.data);

	return (
		<div>
			<h1>Dashboard</h1>
			<p>Welcome {session?.user.name}</p>
			<p>privateData: {me.data?.email}</p>
		</div>
	);
}
